import { useParams } from 'react-router'
import { BAPTISMA_CATEGORY_ID } from '~/lib/constants'
import BaptismaCategory from './baptisma-category'
import BaptismaTable from './baptisma-table'
import useGetDocumentsByCategoryId from './use-get-documents-by-category-id'

export default function CategoryById() {
  const { id } = useParams()
  const { data } = useGetDocumentsByCategoryId({ id })

  return (
    <>
      {id === BAPTISMA_CATEGORY_ID && (
        <>
          <BaptismaCategory />
          <BaptismaTable documents={data?.getDocumentsByCategoryId?.data || []} />
        </>
      )}
    </>
  )
}
