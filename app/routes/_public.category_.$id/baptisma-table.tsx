import type { FragmentType } from '~/gql'
import type { GetDocumentsByCategoryIdQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useFragment } from '~/gql'
import { BAPTISMA_RECORD_FRAGMENT } from './graphql'

type Document = NonNullable<GetDocumentsByCategoryIdQuery['getDocumentsByCategoryId']['data']>[number]

interface Props {
  documents: Document[]
}

// Component to render a single baptisma record row
function BaptismaRecordRow({
  documentId,
  baptismaRecord,
}: {
  documentId: string
  baptismaRecord: FragmentType<typeof BAPTISMA_RECORD_FRAGMENT>
}) {
  // Use the fragment to get typed data
  const record = useFragment(BAPTISMA_RECORD_FRAGMENT, baptismaRecord)

  return (
    <TableRow key={documentId}>
      <TableCell>{record.baptisma_registration_no || '-'}</TableCell>
      <TableCell>{record.hming || '-'}</TableCell>
      <TableCell>{record.pa_hming || '-'}</TableCell>
      <TableCell>{record.nu_hming || '-'}</TableCell>
      <TableCell>{record.khua || '-'}</TableCell>
      <TableCell>
        {record.pian_ni ? format(new Date(record.pian_ni), 'yyyy-MM-dd') : '-'}
      </TableCell>
      <TableCell>
        {record.baptisma_chan_ni ? format(new Date(record.baptisma_chan_ni), 'yyyy-MM-dd') : '-'}
      </TableCell>
      <TableCell>{record.chantirtu || '-'}</TableCell>
    </TableRow>
  )
}

export default function BaptismaTable({ documents }: Props) {
  // Filter documents that have BaptismaRecord as extra_record
  const baptismaDocuments = documents.filter(doc =>
    doc.extra_record
    && doc.extra_record.__typename === 'BaptismaRecord',
  )

  return (
    <div className="my-4 flex grow rounded-md bg-white p-4">
      <Table className="w-full min-w-[1000px] table-fixed">
        <TableHeader>
          <TableRow>
            <TableHead className="w-32">Reg no</TableHead>
            <TableHead className="w-32">Hming</TableHead>
            <TableHead className="w-32">Pa Hming</TableHead>
            <TableHead className="w-32">Nu Hming</TableHead>
            <TableHead className="w-32">Khua</TableHead>
            <TableHead className="w-32">Pian ni</TableHead>
            <TableHead className="w-32">Baptisma chan ni</TableHead>
            <TableHead className="w-32">Chantirtu</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {baptismaDocuments.length > 0
            ? (
                baptismaDocuments.map(document => (
                  <BaptismaRecordRow
                    key={document.id}
                    documentId={document.id}
                    baptismaRecord={document.extra_record}
                  />
                ))
              )
            : (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center text-muted-foreground"
                  >
                    No baptisma records found
                  </TableCell>
                </TableRow>
              )}
        </TableBody>
      </Table>
    </div>
  )
}
