import type { GetSpotlightsQuery } from '~/gql/graphql'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { baseUrl } from '~/lib/base-url'

interface Props {
  spotlight: GetSpotlightsQuery['getSpotlights']['data'][number]
  isOpen: boolean
  toggle: (open: boolean) => void
}

export default function SpotlightModal({ spotlight, isOpen, toggle }: Props) {
  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent className="w-full max-w-3xl min-w-3xl">
        <img
          className="h-96 w-full object-cover"
          src={`${baseUrl}/image/${spotlight.file_path}`}
        />
        <DialogHeader>
          <DialogTitle>
            {spotlight.title}
          </DialogTitle>
          <DialogDescription>
            {spotlight.body}
          </DialogDescription>
        </DialogHeader>
      </DialogContent>

    </Dialog>
  )
}
