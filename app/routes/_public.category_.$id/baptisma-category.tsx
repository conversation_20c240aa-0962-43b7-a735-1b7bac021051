import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { useAppForm } from '~/hooks/form'
import useGetDocumentsByCategoryId from './use-get-documents-by-category-id'

export default function BaptismaCategory() {
  const { searchBaptisma, isLoading } = useGetDocumentsByCategoryId({ id: '4' })

  const form = useAppForm({
    defaultValues: {
      registration_no: '',
      hming: '',
      pa_hming: '',
      nu_hming: '',
      khua: '',
      pian_ni: '',
      baptisma_chan_ni: '',
      chantirtu: '',
    },
    onSubmit: async ({ value }) => {
      searchBaptisma(value)
    },
  })

  return (
    <>
      <Card className="my-4 bg-white">
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4"
          >
            <form.AppField
              name="registration_no"
              children={field => <field.InputField label="Registration No" />}
            />
            <form.AppField
              name="hming"
              children={field => <field.InputField label="Mipa hming" />}
            />
            <form.AppField
              name="pa_hming"
              children={field => <field.InputField label="Pa hming" />}
            />
            <form.AppField
              name="nu_hming"
              children={field => <field.InputField label="Nu hming" />}
            />

            <form.AppField
              name="khua"
              children={field => <field.InputField label="Khua" />}
            />
            <form.AppField
              name="pian_ni"
              children={field => <field.InputField label="Pian ni" type="date" />}
            />
            <form.AppField
              name="baptisma_chan_ni"
              children={field => <field.InputField label="Baptisma chan ni" type="date" />}
            />
            <form.AppField
              name="chantirtu"
              children={field => <field.InputField label="Chantirtu" />}
            />
            <div className="col-span-1">
              <Button type="submit" isLoading={isLoading}>
                Search
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </>
  )
}
