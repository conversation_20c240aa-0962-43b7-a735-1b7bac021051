import { useState } from 'react'
import { Link } from 'react-router'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '~/components/ui/accordion'
import { cn } from '~/lib/utils'
import Spotlight from './spotlight'
import useGetCategories from './use-get-categories'

export default function Home() {
  const { data } = useGetCategories()
  const [selectedMainCategoryId, setSelectedMainCategoryId] = useState<string | null>('2')

  const mainCategories = data?.getCategories?.filter(category => category.parent_id === 1) || []

  // Create structured subcategories for accordion display
  const getSubCategoriesForMainCategory = (mainCategoryId: string) => {
    const mainCategory = mainCategories.find(cat => cat.id === mainCategoryId)
    if (!mainCategory?.children)
      return []

    return mainCategory.children.map(subCategory => ({
      id: subCategory.id,
      name: subCategory.name,
      is_leaf: subCategory.is_leaf,
      is_classified: subCategory.is_classified,
      children: subCategory.children || [],
    }))
  }

  const subCategories = selectedMainCategoryId
    ? getSubCategoriesForMainCategory(selectedMainCategoryId)
    : []

  return (
    <>
      <Spotlight />
      <div className="grid grow grid-cols-12">
        <div className="col-span-4 h-full bg-primary text-white">
          <h2 className="px-8 py-6 text-2xl">CATEGORY</h2>
          <div className="border border-white" />
          <ul className="px-8 py-4">
            {mainCategories.map(category => (
              <li className="py-2 text-lg" key={category.id}>
                {category.id === '4' || category.id === '5'
                  ? (
                      <Link
                        to={`/category/${category.id}`}
                        className="cursor-pointer"
                      >
                        {category.name}
                      </Link>
                    )
                  : (
                      <div
                        onClick={() => {
                          setSelectedMainCategoryId(category.id)
                        }}
                        className={cn(
                          'cursor-pointer',
                          selectedMainCategoryId === category.id && `font-bold`,
                        )}
                      >
                        {category.name}
                      </div>
                    )}
              </li>
            ))}
          </ul>
        </div>

        <div className="col-span-8 bg-white">
          {selectedMainCategoryId && (
            <div className="p-4">
              <h3 className="mb-4 text-lg font-semibold text-gray-800">
                Subcategories
              </h3>

              {subCategories.length > 0
                ? (
                    <Accordion type="multiple" className="w-full">
                      {subCategories.map((subCategory, index) => (
                        <AccordionItem className="border-b-2 border-gold py-2" key={subCategory.id} value={subCategory.id}>
                          <AccordionTrigger
                            className={`
                              text-left
                              hover:no-underline
                            `}
                            childLeft={!subCategory.is_leaf}
                          >
                            {subCategory.is_leaf
                              ? (
                                  <Link
                                    to={`/category/${subCategory.id}`}
                                    onClick={e => e.stopPropagation()}
                                    className="hover:underline"
                                  >
                                    {index + 1}
                                    )
                                    {' '}
                                    {subCategory.name}
                                  </Link>
                                )
                              : (
                                  <span className="text-gray-800">
                                    {index + 1}
                                    )
                                    {' '}
                                    {subCategory.name}
                                  </span>
                                )}
                          </AccordionTrigger>

                          {!subCategory.is_leaf && subCategory.children.length > 0 && (
                            <AccordionContent>
                              <div className="space-y-2 pl-4">
                                {subCategory.children.map((child, index) => (
                                  <div key={child.id} className="py-1">
                                    {child.is_leaf
                                      ? (
                                          <Link
                                            to={`/category/${child.id}`}
                                            className="hover:underline"
                                          >
                                            {index + 1}
                                            )
                                            {' '}
                                            {child.name}
                                          </Link>
                                        )
                                      : (
                                          <span className="text-gray-700">
                                            {index + 1}
                                            )
                                            {' '}
                                            {child.name}
                                          </span>
                                        )}
                                  </div>
                                ))}
                              </div>
                            </AccordionContent>
                          )}
                        </AccordionItem>
                      ))}
                    </Accordion>
                  )
                : (
                    <div className="text-gray-500">No subcategories found for this main category.</div>
                  )}
            </div>
          )}
        </div>
      </div>
    </>
  )
}
